<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google 登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #3367d6;
        }
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            border-left: 4px solid #4285f4;
        }
        .error {
            border-left-color: #ea4335;
            background: #ffeaea;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Google OAuth 登录测试</h1>
    
    <div class="container">
        <h2>配置信息</h2>
        <p><strong>客户端ID:</strong> 262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com</p>
        <p><strong>重定向URI:</strong> http://localhost:3001/auth/google/callback</p>
        <p><strong>API端点:</strong> /admin-api/erp/game/google-login</p>
    </div>

    <div class="container">
        <h2>步骤 1: 获取授权码</h2>
        <p>点击下面的按钮跳转到Google授权页面：</p>
        <button onclick="startGoogleAuth()">开始 Google 授权</button>
        
        <div id="authResult" class="result" style="display: none;">
            <h3>授权结果</h3>
            <p><strong>授权码:</strong> <span id="authCode"></span></p>
            <p><strong>状态:</strong> <span id="authState"></span></p>
        </div>
    </div>

    <div class="container">
        <h2>步骤 2: 测试登录接口</h2>
        <p>输入从Google获取的授权码：</p>
        <input type="text" id="codeInput" placeholder="输入授权码" style="width: 100%; padding: 8px; margin: 10px 0;">
        <input type="text" id="gameIdInput" placeholder="游戏ID (可选)" style="width: 100%; padding: 8px; margin: 10px 0;">
        <input type="text" id="productIdInput" placeholder="产品ID (可选，用逗号分隔)" style="width: 100%; padding: 8px; margin: 10px 0;">
        <button onclick="testLogin()">测试登录</button>
        
        <div id="loginResult" class="result" style="display: none;">
            <h3>登录结果</h3>
            <pre id="loginResponse"></pre>
        </div>
    </div>

    <div class="container">
        <h2>使用说明</h2>
        <ol>
            <li>点击"开始 Google 授权"按钮，会跳转到Google授权页面</li>
            <li>在Google页面完成授权后，会重定向回来并显示授权码</li>
            <li>将授权码复制到输入框中，点击"测试登录"</li>
            <li>查看登录结果</li>
        </ol>
        
        <h3>注意事项</h3>
        <ul>
            <li>确保用户在系统中已经存在（使用Google邮箱或Google ID作为用户名）</li>
            <li>如果指定了产品ID，用户必须属于该产品</li>
            <li>授权码只能使用一次，如果测试失败需要重新获取</li>
        </ul>
    </div>

    <script>
        // Google OAuth 配置
        const GOOGLE_CLIENT_ID = '262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com';
        const REDIRECT_URI = 'http://localhost:3001/auth/google/callback';
        const API_BASE_URL = '/admin-api/erp/game';

        // 生成随机状态值
        function generateRandomState() {
            return Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(36).substring(2, 15);
        }

        // 开始Google授权
        function startGoogleAuth() {
            const state = generateRandomState();
            localStorage.setItem('oauth_state', state);
            
            const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
                `client_id=${GOOGLE_CLIENT_ID}&` +
                `redirect_uri=${encodeURIComponent(REDIRECT_URI)}&` +
                `response_type=code&` +
                `scope=openid email profile&` +
                `state=${state}`;
            
            window.location.href = googleAuthUrl;
        }

        // 检查URL参数中是否有授权码
        function checkAuthCode() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const error = urlParams.get('error');
            
            if (error) {
                document.getElementById('authResult').style.display = 'block';
                document.getElementById('authResult').className = 'result error';
                document.getElementById('authCode').textContent = '授权失败: ' + error;
                return;
            }
            
            if (code) {
                const savedState = localStorage.getItem('oauth_state');
                document.getElementById('authResult').style.display = 'block';
                document.getElementById('authCode').textContent = code;
                document.getElementById('authState').textContent = state === savedState ? '验证通过' : '状态验证失败';
                document.getElementById('codeInput').value = code;
                
                // 清理URL参数
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // 测试登录接口
        async function testLogin() {
            const code = document.getElementById('codeInput').value;
            const gameId = document.getElementById('gameIdInput').value;
            const productIdStr = document.getElementById('productIdInput').value;
            
            if (!code) {
                alert('请输入授权码');
                return;
            }
            
            const requestBody = {
                code: code
            };
            
            if (gameId) {
                requestBody.gameId = parseInt(gameId);
            }
            
            if (productIdStr) {
                requestBody.productId = productIdStr.split(',').map(id => id.trim());
            }
            
            try {
                const response = await fetch(API_BASE_URL + '/google-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                document.getElementById('loginResult').style.display = 'block';
                document.getElementById('loginResult').className = result.code === 0 ? 'result' : 'result error';
                document.getElementById('loginResponse').textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                document.getElementById('loginResult').style.display = 'block';
                document.getElementById('loginResult').className = 'result error';
                document.getElementById('loginResponse').textContent = '请求失败: ' + error.message;
            }
        }

        // 页面加载时检查授权码
        window.onload = function() {
            checkAuthCode();
        };
    </script>
</body>
</html>
