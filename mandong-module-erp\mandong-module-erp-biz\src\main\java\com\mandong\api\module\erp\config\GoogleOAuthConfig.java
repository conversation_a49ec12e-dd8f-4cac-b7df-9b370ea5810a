package com.mandong.api.module.erp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Google OAuth 配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "google.oauth")
public class GoogleOAuthConfig {

    /**
     * Google OAuth 客户端ID
     */
    private String clientId = "262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com";

    /**
     * Google OAuth 客户端密钥
     */
    private String clientSecret = "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";

    /**
     * 重定向URI
     */
    private String redirectUri = "";

    /**
     * Google OAuth Token 端点
     */
    private String tokenUrl = "https://www.googleapis.com/oauth2/v4/token";

    /**
     * Google 用户信息端点
     */
    private String userInfoUrl = "https://www.googleapis.com/oauth2/v2/userinfo";
}
