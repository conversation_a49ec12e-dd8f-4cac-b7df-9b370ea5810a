<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mandong.api</groupId>
        <artifactId>mandong-module-erp</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mandong-module-erp-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        erp 包下，企业资源管理（Enterprise Resource Planning）。
        例如说：采购、销售、库存、财务、产品等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-module-erp-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>6.0.0-M19</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mandong.api</groupId>
            <artifactId>mandong-spring-boot-starter-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.43</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>