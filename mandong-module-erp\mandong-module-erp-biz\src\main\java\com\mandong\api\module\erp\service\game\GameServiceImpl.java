package com.mandong.api.module.erp.service.game;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSONFactory;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.JsonObjectParser;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.*;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LOGIN_FAILED;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

@Service
public class GameServiceImpl implements GameService {

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkPackGameApkMapper sdkPackGameApkMapper;

    @Resource
    private SdkPackTaskMapper sdkPackTaskMapper;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private SdkAdPageMapper sdkAdPageMapper;

    @Resource
    private SdkShortHostsMapper sdkShortHostsMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;



    @Override
    public PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO) {
        return sdkProductMapper.selectPage(pageReqVO);
    }

    @Override
    public GameGetUrlRespVO getUrl(Integer id) {
        return null;
    }

    @Override
    public List<GameVersionRespVO> getGameVersion(Integer id) {

        return sdkPackGameApkMapper.selectGameVersion(id);
    }

    @Override
    public PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        List<SdkOperationCondition> operationPermittedConditions = null;
        // 如果是运营人员
        if (roleId.equals("163")) {
            // 获取用户权限范围内的产品和渠道条件
            operationPermittedConditions = orderService.getOperationUserPermittedConditions(userId);
            if (CollectionUtils.isEmpty(operationPermittedConditions)) {
                return new PageResult<>(new ArrayList<>(),0L);
            }
        }

        return sdkPackTaskMapper.getTaskPage(reqVO,operationPermittedConditions);
    }

    @Override
    public List<SdkShortHostsDO> getShortHost() {
        return sdkShortHostsMapper.selectList();
    }



    @Override
    public List<SdkAdPageDO> getAdPage(Integer id) {


        return sdkAdPageMapper.getAdPage(id);
    }

    @Override
    public UserDetailRespVO login(GameLoginReqVO reqVO) {

        String password = reqVO.getPassword();
        String username = reqVO.getUsername();
        String productId = reqVO.getProductId();

        String[] productIds = productId.split(",");


        SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUsername, username)
                .in(SdkUserDO::getProductId, Arrays.stream(productIds).toArray()));
        if (sdkUserDO == null) {
            throw exception(LOGIN_FAILED);
        }

        MD5 md5 = new MD5();
        String sPassword = md5.digestHex(password);
        String key = sPassword + sdkUserDO.getSlat();

        SdkUserDO userDO = sdkUserMapper.selectOne(SdkUserDO::getUsername, username, SdkUserDO::getPassword, md5.digestHex(key));
        if (userDO == null) {
            throw exception(LOGIN_FAILED);
        }

        SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, userDO.getProductId());
        UserDetailRespVO userDetailRespVO = BeanUtils.toBean(userDO, UserDetailRespVO.class);
        userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl()).setProductCode(sdkProductDO.getProductCode()).setCallbackKey(sdkProductDO.getCallbackKey());


        return userDetailRespVO;
    }

    @Override
    public UserDetailRespVO google_login(GameGoogleReqVO reqVO) {

        JsonObjectParser jsonObjectParser = new JsonObjectParser();
        JSONFactory jsonFactory = new JSONFactory();
        GoogleTokenResponse tokenResponse = new GoogleAuthorizationCodeTokenRequest(
                GoogleNetHttpTransport.newTrustedTransport(), jsonFactory,
                "https://www.googleapis.com/oauth2/v4/token",
                "262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com",
                "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",
                reqVO.getCode(),
                ""
        ).execute;

        // 验证ID Token
        GoogleIdToken idToken = GoogleIdToken.parse(
                jsonFactory,
                tokenResponse.getIdToken()
        );

        GoogleIdToken.Payload payload = idToken.getPayload;
        String email = payload.getEmail;
        String userId = payload.getSubject;

        // 存储用户信息到数据库
        return "登录成功: " + email;
        return null;
    }
}
