package com.mandong.api.module.erp.service.game;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeTokenRequest;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import lombok.extern.slf4j.Slf4j;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.config.GoogleOAuthConfig;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.*;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LOGIN_FAILED;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

@Service
@Slf4j
public class GameServiceImpl implements GameService {

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkPackGameApkMapper sdkPackGameApkMapper;

    @Resource
    private SdkPackTaskMapper sdkPackTaskMapper;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private SdkAdPageMapper sdkAdPageMapper;

    @Resource
    private SdkShortHostsMapper sdkShortHostsMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private GoogleOAuthConfig googleOAuthConfig;



    @Override
    public PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO) {
        return sdkProductMapper.selectPage(pageReqVO);
    }

    @Override
    public GameGetUrlRespVO getUrl(Integer id) {
        return null;
    }

    @Override
    public List<GameVersionRespVO> getGameVersion(Integer id) {

        return sdkPackGameApkMapper.selectGameVersion(id);
    }

    @Override
    public PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        List<SdkOperationCondition> operationPermittedConditions = null;
        // 如果是运营人员
        if (roleId.equals("163")) {
            // 获取用户权限范围内的产品和渠道条件
            operationPermittedConditions = orderService.getOperationUserPermittedConditions(userId);
            if (CollectionUtils.isEmpty(operationPermittedConditions)) {
                return new PageResult<>(new ArrayList<>(),0L);
            }
        }

        return sdkPackTaskMapper.getTaskPage(reqVO,operationPermittedConditions);
    }

    @Override
    public List<SdkShortHostsDO> getShortHost() {
        return sdkShortHostsMapper.selectList();
    }



    @Override
    public List<SdkAdPageDO> getAdPage(Integer id) {


        return sdkAdPageMapper.getAdPage(id);
    }

    @Override
    public UserDetailRespVO login(GameLoginReqVO reqVO) {

        String password = reqVO.getPassword();
        String username = reqVO.getUsername();
        String productId = reqVO.getProductId();

        String[] productIds = productId.split(",");


        SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUsername, username)
                .in(SdkUserDO::getProductId, Arrays.stream(productIds).toArray()));
        if (sdkUserDO == null) {
            throw exception(LOGIN_FAILED);
        }

        MD5 md5 = new MD5();
        String sPassword = md5.digestHex(password);
        String key = sPassword + sdkUserDO.getSlat();

        SdkUserDO userDO = sdkUserMapper.selectOne(SdkUserDO::getUsername, username, SdkUserDO::getPassword, md5.digestHex(key));
        if (userDO == null) {
            throw exception(LOGIN_FAILED);
        }

        SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, userDO.getProductId());
        UserDetailRespVO userDetailRespVO = BeanUtils.toBean(userDO, UserDetailRespVO.class);
        userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl()).setProductCode(sdkProductDO.getProductCode()).setCallbackKey(sdkProductDO.getCallbackKey());


        return userDetailRespVO;
    }

    @Override
    public UserDetailRespVO google_login(GameGoogleReqVO reqVO) {
        try {
            // 1. 使用授权码获取访问令牌
            GsonFactory jsonFactory = GsonFactory.getDefaultInstance();
            GoogleTokenResponse tokenResponse = new GoogleAuthorizationCodeTokenRequest(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    jsonFactory,
                    googleOAuthConfig.getClientId(),
                    googleOAuthConfig.getClientSecret(),
                    reqVO.getCode(),
                    googleOAuthConfig.getRedirectUri()
            ).execute();

            // 2. 验证并解析ID Token
            GoogleIdToken idToken = GoogleIdToken.parse(
                    jsonFactory,
                    tokenResponse.getIdToken()
            );

            GoogleIdToken.Payload payload = idToken.getPayload();
            String email = payload.getEmail();
            String googleUserId = payload.getSubject();

            // 3. 根据Google用户信息查找系统中的用户
            // 优先使用email查找，其次使用Google ID
            SdkUserDO sdkUserDO = findUserByGoogleInfo(email, googleUserId, reqVO.getProductId());

            if (sdkUserDO == null) {
                // 如果找不到用户，抛出登录失败异常
                throw exception(LOGIN_FAILED);
            }

            // 4. 获取产品信息
            SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, sdkUserDO.getProductId());

            // 5. 构建返回结果
            UserDetailRespVO userDetailRespVO = BeanUtils.toBean(sdkUserDO, UserDetailRespVO.class);
            userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl())
                           .setProductCode(sdkProductDO.getProductCode())
                           .setCallbackKey(sdkProductDO.getCallbackKey());

            return userDetailRespVO;

        } catch (Exception e) {
            // 记录详细错误日志
            log.error("Google登录失败，授权码: {}, 错误信息: {}", reqVO.getCode(), e.getMessage(), e);
            throw exception(LOGIN_FAILED);
        }
    }

    /**
     * 根据Google用户信息查找系统用户
     * @param email Google用户邮箱
     * @param googleUserId Google用户ID
     * @param productIds 产品ID数组
     * @return 用户信息
     */
    private SdkUserDO findUserByGoogleInfo(String email, String googleUserId, String[] productIds) {
        SdkUserDO user = null;

        // 1. 优先使用email查找用户（如果email不为空）
        if (email != null && !email.trim().isEmpty()) {
            user = findUserByUsername(email, productIds);
        }

        // 2. 如果通过email没找到，使用Google ID查找
        if (user == null && googleUserId != null && !googleUserId.trim().isEmpty()) {
            user = findUserByUsername(googleUserId, productIds);
        }

        return user;
    }

    /**
     * 根据用户名查找系统用户
     * @param username 用户名
     * @param productIds 产品ID数组
     * @return 用户信息
     */
    private SdkUserDO findUserByUsername(String username, String[] productIds) {
        // 构建查询条件
        MPJLambdaWrapperX<SdkUserDO> wrapper = new MPJLambdaWrapperX<SdkUserDO>()
                .eq(SdkUserDO::getUsername, username);

        // 如果指定了产品ID，则添加产品ID过滤条件
        if (productIds != null && productIds.length > 0) {
            wrapper.in(SdkUserDO::getProductId, Arrays.stream(productIds).map(Long::valueOf).toArray());
        }

        return sdkUserMapper.selectOne(wrapper);
    }
}
