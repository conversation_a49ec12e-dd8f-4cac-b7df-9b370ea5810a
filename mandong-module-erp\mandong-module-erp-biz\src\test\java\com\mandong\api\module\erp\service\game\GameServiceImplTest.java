package com.mandong.api.module.erp.service.game;

import com.mandong.api.framework.test.core.ut.BaseDbUnitTest;
import com.mandong.api.module.erp.config.GoogleOAuthConfig;
import com.mandong.api.module.erp.controller.admin.game.vo.GameGoogleReqVO;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * {@link GameServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(GameServiceImpl.class)
class GameServiceImplTest extends BaseDbUnitTest {

    @Resource
    private GameServiceImpl gameService;

    @MockBean
    private SdkUserMapper sdkUserMapper;
    
    @MockBean
    private SdkProductMapper sdkProductMapper;
    
    @MockBean
    private GoogleOAuthConfig googleOAuthConfig;

    @Test
    void testGoogleLoginConfigurationLoaded() {
        // 测试配置是否正确加载
        when(googleOAuthConfig.getClientId()).thenReturn("test-client-id");
        when(googleOAuthConfig.getClientSecret()).thenReturn("test-client-secret");
        when(googleOAuthConfig.getRedirectUri()).thenReturn("http://localhost:3001/auth/google/callback");
        
        assertEquals("test-client-id", googleOAuthConfig.getClientId());
        assertEquals("test-client-secret", googleOAuthConfig.getClientSecret());
        assertEquals("http://localhost:3001/auth/google/callback", googleOAuthConfig.getRedirectUri());
    }

    @Test
    void testGoogleLoginWithInvalidCode() {
        // 测试无效授权码的情况
        GameGoogleReqVO reqVO = new GameGoogleReqVO();
        reqVO.setCode("invalid-code");
        reqVO.setProductId(new String[]{"1"});
        
        // 由于Google API调用会失败，应该抛出登录失败异常
        assertThrows(Exception.class, () -> {
            gameService.google_login(reqVO);
        });
    }

    // 注意：由于Google OAuth需要真实的网络请求，完整的集成测试需要在实际环境中进行
    // 这里主要测试配置和基本的错误处理逻辑
}
